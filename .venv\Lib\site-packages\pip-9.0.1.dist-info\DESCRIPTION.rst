pip
===

The `PyPA recommended
<https://packaging.python.org/en/latest/current/>`_
tool for installing Python packages.

* `Installation <https://pip.pypa.io/en/stable/installing.html>`_
* `Documentation <https://pip.pypa.io/>`_
* `Changelog <https://pip.pypa.io/en/stable/news.html>`_
* `Github Page <https://github.com/pypa/pip>`_
* `Issue Tracking <https://github.com/pypa/pip/issues>`_
* `User mailing list <http://groups.google.com/group/python-virtualenv>`_
* `Dev mailing list <http://groups.google.com/group/pypa-dev>`_
* User IRC: #pypa on Freenode.
* Dev IRC: #pypa-dev on Freenode.


.. image:: https://img.shields.io/pypi/v/pip.svg
   :target: https://pypi.python.org/pypi/pip

.. image:: https://img.shields.io/travis/pypa/pip/master.svg
   :target: http://travis-ci.org/pypa/pip

.. image:: https://img.shields.io/appveyor/ci/pypa/pip.svg
   :target: https://ci.appveyor.com/project/pypa/pip/history

.. image:: https://readthedocs.org/projects/pip/badge/?version=stable
   :target: https://pip.pypa.io/en/stable

Code of Conduct
---------------

Everyone interacting in the pip project's codebases, issue trackers, chat
rooms, and mailing lists is expected to follow the `PyPA Code of Conduct`_.

.. _PyPA Code of Conduct: https://www.pypa.io/en/latest/code-of-conduct/


